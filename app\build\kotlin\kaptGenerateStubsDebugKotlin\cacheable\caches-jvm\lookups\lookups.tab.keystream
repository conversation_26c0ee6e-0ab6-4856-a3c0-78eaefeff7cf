  Manifest android  Activity android.app  Application android.app  Bundle android.app.Activity  
CameraXConfig android.app.Application  getSharedPreferences android.app.Application  Context android.content  SharedPreferences android.content  Bundle android.content.Context  
CameraXConfig android.content.Context  MODE_PRIVATE android.content.Context  filesDir android.content.Context  getFILESDir android.content.Context  getFilesDir android.content.Context  getSharedPreferences android.content.Context  setFilesDir android.content.Context  Bundle android.content.ContextWrapper  
CameraXConfig android.content.ContextWrapper  getSharedPreferences android.content.ContextWrapper  PackageManager android.content.pm  	Resources android.content.res  Bitmap android.graphics  
BitmapFactory android.graphics  ImageDecoder android.graphics  Typeface android.graphics  equals android.graphics.Bitmap  BitmapDrawable android.graphics.drawable  Uri android.net  equals android.net.Uri  getLET android.net.Uri  getLet android.net.Uri  let android.net.Uri  parse android.net.Uri  toString android.net.Uri  Build 
android.os  Bundle 
android.os  
Parcelable 
android.os  
MediaStore android.provider  TextToSpeech android.speech.tts  Log android.util  d android.util.Log  e android.util.Log  w android.util.Log  Bundle  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  ActivityResultContracts !androidx.activity.result.contract  
Camera2Config androidx.camera.camera2  CameraSelector androidx.camera.core  
CameraXConfig androidx.camera.core  ImageCapture androidx.camera.core  ImageCaptureException androidx.camera.core  Preview androidx.camera.core  Provider "androidx.camera.core.CameraXConfig  ProcessCameraProvider androidx.camera.lifecycle  PreviewView androidx.camera.view  	ScaleType  androidx.camera.view.PreviewView  AnimatedVisibility androidx.compose.animation  
Composable androidx.compose.animation  	Crossfade androidx.compose.animation  ExperimentalAnimationApi androidx.compose.animation  ExperimentalMaterial3Api androidx.compose.animation  androidx androidx.compose.animation  fadeIn androidx.compose.animation  fadeOut androidx.compose.animation  slideInHorizontally androidx.compose.animation  slideInVertically androidx.compose.animation  
Animatable androidx.compose.animation.core  
Composable androidx.compose.animation.core  ExperimentalMaterial3Api androidx.compose.animation.core  LinearEasing androidx.compose.animation.core  
RepeatMode androidx.compose.animation.core  androidx androidx.compose.animation.core  animateFloat androidx.compose.animation.core  animateFloatAsState androidx.compose.animation.core  infiniteRepeatable androidx.compose.animation.core  rememberInfiniteTransition androidx.compose.animation.core  tween androidx.compose.animation.core  Canvas androidx.compose.foundation  Image androidx.compose.foundation  
background androidx.compose.foundation  border androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  MutableInteractionSource 'androidx.compose.foundation.interaction  collectIsPressedAsState 'androidx.compose.foundation.interaction  Arrangement "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  ExperimentalMaterialApi "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  
LazyColumn  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  ExperimentalMaterialApi androidx.compose.material  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  	Analytics ,androidx.compose.material.icons.Icons.Filled  	CameraAlt ,androidx.compose.material.icons.Icons.Filled  History ,androidx.compose.material.icons.Icons.Filled  Home ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  	ArrowBack 3androidx.compose.material.icons.automirrored.filled  ArrowForward 3androidx.compose.material.icons.automirrored.filled  HelpOutline 3androidx.compose.material.icons.automirrored.filled  	Analytics &androidx.compose.material.icons.filled  	ArrowBack &androidx.compose.material.icons.filled  	CameraAlt &androidx.compose.material.icons.filled  CheckCircle &androidx.compose.material.icons.filled  Close &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  ExperimentalMaterialApi &androidx.compose.material.icons.filled  History &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  Image &androidx.compose.material.icons.filled  Kitchen &androidx.compose.material.icons.filled  
Nightlight &androidx.compose.material.icons.filled  PhotoCamera &androidx.compose.material.icons.filled  Replay &androidx.compose.material.icons.filled  Science &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  ThumbUp &androidx.compose.material.icons.filled  Warning &androidx.compose.material.icons.filled  WbSunny &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  PullRefreshIndicator %androidx.compose.material.pullrefresh  pullRefresh %androidx.compose.material.pullrefresh  rememberPullRefreshState %androidx.compose.material.pullrefresh  Button androidx.compose.material3  ButtonDefaults androidx.compose.material3  Card androidx.compose.material3  CardDefaults androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ColorScheme androidx.compose.material3  
Composable androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  ExperimentalMaterialApi androidx.compose.material3  FilledTonalButton androidx.compose.material3  HorizontalDivider androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  
MaterialTheme androidx.compose.material3  
NavigationBar androidx.compose.material3  NavigationBarItem androidx.compose.material3  NavigationBarItemDefaults androidx.compose.material3  OutlinedTextField androidx.compose.material3  Scaffold androidx.compose.material3  Surface androidx.compose.material3  Text androidx.compose.material3  
TextButton androidx.compose.material3  	TopAppBar androidx.compose.material3  TopAppBarDefaults androidx.compose.material3  
Typography androidx.compose.material3  androidx androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  
Composable androidx.compose.runtime  DisposableEffect androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  ExperimentalMaterialApi androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
SideEffect androidx.compose.runtime  androidx androidx.compose.runtime  collectAsState androidx.compose.runtime  getValue androidx.compose.runtime  mutableStateOf androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  Saver !androidx.compose.runtime.saveable  
SaverScope !androidx.compose.runtime.saveable  rememberSaveable !androidx.compose.runtime.saveable  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  alpha androidx.compose.ui.draw  clip androidx.compose.ui.draw  scale androidx.compose.ui.draw  CornerRadius androidx.compose.ui.geometry  Offset androidx.compose.ui.geometry  Rect androidx.compose.ui.geometry  Size androidx.compose.ui.geometry  	BlendMode androidx.compose.ui.graphics  Brush androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  Path androidx.compose.ui.graphics  	StrokeCap androidx.compose.ui.graphics  
asImageBitmap androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  invoke ,androidx.compose.ui.graphics.Color.Companion  Stroke &androidx.compose.ui.graphics.drawscope  ImageVector #androidx.compose.ui.graphics.vector  ContentScale androidx.compose.ui.layout  LocalContext androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  painterResource androidx.compose.ui.res  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  PasswordVisualTransformation androidx.compose.ui.text.input  	TextAlign androidx.compose.ui.text.style  TextOverflow androidx.compose.ui.text.style  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  AndroidView androidx.compose.ui.viewinterop  Bundle #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  FileProvider androidx.core.content  WindowCompat androidx.core.view  AndroidViewModel androidx.lifecycle  	ViewModel androidx.lifecycle  ViewModelProvider androidx.lifecycle  viewModelScope androidx.lifecycle  Application #androidx.lifecycle.AndroidViewModel  Bitmap #androidx.lifecycle.AndroidViewModel  Boolean #androidx.lifecycle.AndroidViewModel  DatabaseImageFetcher #androidx.lifecycle.AndroidViewModel  FirebaseData #androidx.lifecycle.AndroidViewModel  	GeminiApi #androidx.lifecycle.AndroidViewModel  HistoryRepository #androidx.lifecycle.AndroidViewModel  ImageLoader #androidx.lifecycle.AndroidViewModel  List #androidx.lifecycle.AndroidViewModel  MutableStateFlow #androidx.lifecycle.AndroidViewModel  
ScanResult #androidx.lifecycle.AndroidViewModel  SharingStarted #androidx.lifecycle.AndroidViewModel  	StateFlow #androidx.lifecycle.AndroidViewModel  Uri #androidx.lifecycle.AndroidViewModel  _isHistoryLoading #androidx.lifecycle.AndroidViewModel  com #androidx.lifecycle.AndroidViewModel  	emptyList #androidx.lifecycle.AndroidViewModel  invoke #androidx.lifecycle.AndroidViewModel  onEach #androidx.lifecycle.AndroidViewModel  onStart #androidx.lifecycle.AndroidViewModel  stateIn #androidx.lifecycle.AndroidViewModel  viewModelScope #androidx.lifecycle.AndroidViewModel  Application androidx.lifecycle.ViewModel  Bitmap androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  Context androidx.lifecycle.ViewModel  DatabaseImageFetcher androidx.lifecycle.ViewModel  FirebaseData androidx.lifecycle.ViewModel  	GeminiApi androidx.lifecycle.ViewModel  HistoryRepository androidx.lifecycle.ViewModel  ImageLoader androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  
ScanResult androidx.lifecycle.ViewModel  SharingStarted androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  Uri androidx.lifecycle.ViewModel  _isHistoryLoading androidx.lifecycle.ViewModel  com androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  invoke androidx.lifecycle.ViewModel  onEach androidx.lifecycle.ViewModel  onStart androidx.lifecycle.ViewModel  stateIn androidx.lifecycle.ViewModel  viewModelScope androidx.lifecycle.ViewModel  Factory $androidx.lifecycle.ViewModelProvider  LocalLifecycleOwner androidx.lifecycle.compose  	viewModel $androidx.lifecycle.viewmodel.compose  
NavController androidx.navigation  NavGraph androidx.navigation  NavHostController androidx.navigation  	Companion androidx.navigation.NavGraph  findStartDestination &androidx.navigation.NavGraph.Companion  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  currentBackStackEntryAsState androidx.navigation.compose  rememberNavController androidx.navigation.compose  AnalysisEntity 
androidx.room  Dao 
androidx.room  Database 
androidx.room  
DateConverter 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  RoomDatabase 
androidx.room  StringListConverter 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  Volatile 
androidx.room  java 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  AnalysisDao androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  	Migration androidx.room.RoomDatabase  SupportSQLiteDatabase androidx.room.RoomDatabase  TomatoScanDatabase androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  analysisDao androidx.room.RoomDatabase  	Migration androidx.room.migration  SupportSQLiteDatabase !androidx.room.migration.Migration  SupportSQLiteDatabase androidx.sqlite.db  ImageLoader coil  Builder coil.ComponentRegistry  DatabaseImageFetcher coil.ComponentRegistry.Builder  add coil.ComponentRegistry.Builder  Builder coil.ImageLoader  build coil.ImageLoader.Builder  
components coil.ImageLoader.Builder  
AsyncImage coil.compose  
DataSource coil.decode  DrawableResult 
coil.fetch  FetchResult 
coil.fetch  Fetcher 
coil.fetch  Factory coil.fetch.Fetcher  ImageRequest coil.request  Options coil.request  BarChart #com.github.mikephil.charting.charts  PieChart #com.github.mikephil.charting.charts  Legend 'com.github.mikephil.charting.components  XAxis 'com.github.mikephil.charting.components  BarData !com.github.mikephil.charting.data  
BarDataSet !com.github.mikephil.charting.data  BarEntry !com.github.mikephil.charting.data  PieData !com.github.mikephil.charting.data  
PieDataSet !com.github.mikephil.charting.data  PieEntry !com.github.mikephil.charting.data  IndexAxisValueFormatter &com.github.mikephil.charting.formatter  PercentFormatter &com.github.mikephil.charting.formatter  GenerativeModel !com.google.ai.client.generativeai  content &com.google.ai.client.generativeai.type  FirebaseAuth com.google.firebase.auth  getInstance %com.google.firebase.auth.FirebaseAuth  FirebaseFirestore com.google.firebase.firestore  getInstance /com.google.firebase.firestore.FirebaseFirestore  FirebaseStorage com.google.firebase.storage  getInstance +com.google.firebase.storage.FirebaseStorage  ExperimentalAnimationApi com.ml.tomatoscan  MainActivity com.ml.tomatoscan  
Navigation com.ml.tomatoscan  OptIn com.ml.tomatoscan  R com.ml.tomatoscan  TomatoScanApplication com.ml.tomatoscan  Bundle com.ml.tomatoscan.MainActivity  
CameraXConfig 'com.ml.tomatoscan.TomatoScanApplication  AnalysisEntity com.ml.tomatoscan.data  Any com.ml.tomatoscan.data  Boolean com.ml.tomatoscan.data  	ByteArray com.ml.tomatoscan.data  Date com.ml.tomatoscan.data  Dispatchers com.ml.tomatoscan.data  	Exception com.ml.tomatoscan.data  File com.ml.tomatoscan.data  FirebaseAuth com.ml.tomatoscan.data  FirebaseData com.ml.tomatoscan.data  FirebaseFirestore com.ml.tomatoscan.data  FirebaseStorage com.ml.tomatoscan.data  Float com.ml.tomatoscan.data  	GeminiApi com.ml.tomatoscan.data  GenerativeModel com.ml.tomatoscan.data  HistoryRepository com.ml.tomatoscan.data  ImageStorageHelper com.ml.tomatoscan.data  Int com.ml.tomatoscan.data  List com.ml.tomatoscan.data  Log com.ml.tomatoscan.data  Long com.ml.tomatoscan.data  Pair com.ml.tomatoscan.data  String com.ml.tomatoscan.data  TomatoAnalysisResult com.ml.tomatoscan.data  TomatoScanDatabase com.ml.tomatoscan.data  analysisDao com.ml.tomatoscan.data  apply com.ml.tomatoscan.data  cleanupOldAnalyses com.ml.tomatoscan.data  emptyMap com.ml.tomatoscan.data  first com.ml.tomatoscan.data  forEach com.ml.tomatoscan.data  getValue com.ml.tomatoscan.data  imageStorageHelper com.ml.tomatoscan.data  lazy com.ml.tomatoscan.data  let com.ml.tomatoscan.data  mapOf com.ml.tomatoscan.data  provideDelegate com.ml.tomatoscan.data  to com.ml.tomatoscan.data  withContext com.ml.tomatoscan.data  FirebaseAuth #com.ml.tomatoscan.data.FirebaseData  FirebaseFirestore #com.ml.tomatoscan.data.FirebaseData  FirebaseStorage #com.ml.tomatoscan.data.FirebaseData  List #com.ml.tomatoscan.data.FirebaseData  
ScanResult #com.ml.tomatoscan.data.FirebaseData  String #com.ml.tomatoscan.data.FirebaseData  Uri #com.ml.tomatoscan.data.FirebaseData  Bitmap  com.ml.tomatoscan.data.GeminiApi  	Exception  com.ml.tomatoscan.data.GeminiApi  Float  com.ml.tomatoscan.data.GeminiApi  GenerativeModel  com.ml.tomatoscan.data.GeminiApi  	JSONArray  com.ml.tomatoscan.data.GeminiApi  List  com.ml.tomatoscan.data.GeminiApi  Log  com.ml.tomatoscan.data.GeminiApi  Pair  com.ml.tomatoscan.data.GeminiApi  String  com.ml.tomatoscan.data.GeminiApi  TomatoAnalysisResult  com.ml.tomatoscan.data.GeminiApi  getValue  com.ml.tomatoscan.data.GeminiApi  lazy  com.ml.tomatoscan.data.GeminiApi  provideDelegate  com.ml.tomatoscan.data.GeminiApi  API_KEY *com.ml.tomatoscan.data.GeminiApi.Companion  Bitmap *com.ml.tomatoscan.data.GeminiApi.Companion  	Exception *com.ml.tomatoscan.data.GeminiApi.Companion  Float *com.ml.tomatoscan.data.GeminiApi.Companion  GenerativeModel *com.ml.tomatoscan.data.GeminiApi.Companion  	JSONArray *com.ml.tomatoscan.data.GeminiApi.Companion  List *com.ml.tomatoscan.data.GeminiApi.Companion  Log *com.ml.tomatoscan.data.GeminiApi.Companion  Pair *com.ml.tomatoscan.data.GeminiApi.Companion  String *com.ml.tomatoscan.data.GeminiApi.Companion  TomatoAnalysisResult *com.ml.tomatoscan.data.GeminiApi.Companion  getGETValue *com.ml.tomatoscan.data.GeminiApi.Companion  getGetValue *com.ml.tomatoscan.data.GeminiApi.Companion  getLAZY *com.ml.tomatoscan.data.GeminiApi.Companion  getLazy *com.ml.tomatoscan.data.GeminiApi.Companion  getPROVIDEDelegate *com.ml.tomatoscan.data.GeminiApi.Companion  getProvideDelegate *com.ml.tomatoscan.data.GeminiApi.Companion  getValue *com.ml.tomatoscan.data.GeminiApi.Companion  invoke *com.ml.tomatoscan.data.GeminiApi.Companion  lazy *com.ml.tomatoscan.data.GeminiApi.Companion  provideDelegate *com.ml.tomatoscan.data.GeminiApi.Companion  AnalysisEntity (com.ml.tomatoscan.data.HistoryRepository  Any (com.ml.tomatoscan.data.HistoryRepository  Bitmap (com.ml.tomatoscan.data.HistoryRepository  	ByteArray (com.ml.tomatoscan.data.HistoryRepository  Context (com.ml.tomatoscan.data.HistoryRepository  Date (com.ml.tomatoscan.data.HistoryRepository  Dispatchers (com.ml.tomatoscan.data.HistoryRepository  	Exception (com.ml.tomatoscan.data.HistoryRepository  Flow (com.ml.tomatoscan.data.HistoryRepository  ImageStorageHelper (com.ml.tomatoscan.data.HistoryRepository  List (com.ml.tomatoscan.data.HistoryRepository  Log (com.ml.tomatoscan.data.HistoryRepository  
ScanResult (com.ml.tomatoscan.data.HistoryRepository  String (com.ml.tomatoscan.data.HistoryRepository  TomatoScanDatabase (com.ml.tomatoscan.data.HistoryRepository  Uri (com.ml.tomatoscan.data.HistoryRepository  analysisDao (com.ml.tomatoscan.data.HistoryRepository  cleanupOldAnalyses (com.ml.tomatoscan.data.HistoryRepository  context (com.ml.tomatoscan.data.HistoryRepository  database (com.ml.tomatoscan.data.HistoryRepository  emptyMap (com.ml.tomatoscan.data.HistoryRepository  first (com.ml.tomatoscan.data.HistoryRepository  getEMPTYMap (com.ml.tomatoscan.data.HistoryRepository  getEmptyMap (com.ml.tomatoscan.data.HistoryRepository  getFIRST (com.ml.tomatoscan.data.HistoryRepository  getFirst (com.ml.tomatoscan.data.HistoryRepository  
getHistory (com.ml.tomatoscan.data.HistoryRepository  getLET (com.ml.tomatoscan.data.HistoryRepository  getLet (com.ml.tomatoscan.data.HistoryRepository  getMAPOf (com.ml.tomatoscan.data.HistoryRepository  getMapOf (com.ml.tomatoscan.data.HistoryRepository  getTO (com.ml.tomatoscan.data.HistoryRepository  getTo (com.ml.tomatoscan.data.HistoryRepository  getWITHContext (com.ml.tomatoscan.data.HistoryRepository  getWithContext (com.ml.tomatoscan.data.HistoryRepository  imageStorageHelper (com.ml.tomatoscan.data.HistoryRepository  let (com.ml.tomatoscan.data.HistoryRepository  mapOf (com.ml.tomatoscan.data.HistoryRepository  to (com.ml.tomatoscan.data.HistoryRepository  withContext (com.ml.tomatoscan.data.HistoryRepository  Bitmap )com.ml.tomatoscan.data.ImageStorageHelper  Boolean )com.ml.tomatoscan.data.ImageStorageHelper  	ByteArray )com.ml.tomatoscan.data.ImageStorageHelper  Context )com.ml.tomatoscan.data.ImageStorageHelper  File )com.ml.tomatoscan.data.ImageStorageHelper  Int )com.ml.tomatoscan.data.ImageStorageHelper  Long )com.ml.tomatoscan.data.ImageStorageHelper  String )com.ml.tomatoscan.data.ImageStorageHelper  Uri )com.ml.tomatoscan.data.ImageStorageHelper  apply )com.ml.tomatoscan.data.ImageStorageHelper  bitmapToByteArray )com.ml.tomatoscan.data.ImageStorageHelper  cleanupOldImages )com.ml.tomatoscan.data.ImageStorageHelper  context )com.ml.tomatoscan.data.ImageStorageHelper  deleteImageFile )com.ml.tomatoscan.data.ImageStorageHelper  getAPPLY )com.ml.tomatoscan.data.ImageStorageHelper  getApply )com.ml.tomatoscan.data.ImageStorageHelper  getGETValue )com.ml.tomatoscan.data.ImageStorageHelper  getGetValue )com.ml.tomatoscan.data.ImageStorageHelper  getLAZY )com.ml.tomatoscan.data.ImageStorageHelper  getLazy )com.ml.tomatoscan.data.ImageStorageHelper  getPROVIDEDelegate )com.ml.tomatoscan.data.ImageStorageHelper  getProvideDelegate )com.ml.tomatoscan.data.ImageStorageHelper  getValue )com.ml.tomatoscan.data.ImageStorageHelper  lazy )com.ml.tomatoscan.data.ImageStorageHelper  provideDelegate )com.ml.tomatoscan.data.ImageStorageHelper  saveImageToInternalStorage )com.ml.tomatoscan.data.ImageStorageHelper  uriToByteArray )com.ml.tomatoscan.data.ImageStorageHelper  Float +com.ml.tomatoscan.data.TomatoAnalysisResult  List +com.ml.tomatoscan.data.TomatoAnalysisResult  String +com.ml.tomatoscan.data.TomatoAnalysisResult  AnalysisEntity com.ml.tomatoscan.data.database  Database com.ml.tomatoscan.data.database  
DateConverter com.ml.tomatoscan.data.database  RoomDatabase com.ml.tomatoscan.data.database  StringListConverter com.ml.tomatoscan.data.database  TomatoScanDatabase com.ml.tomatoscan.data.database  TypeConverters com.ml.tomatoscan.data.database  Volatile com.ml.tomatoscan.data.database  AnalysisDao 2com.ml.tomatoscan.data.database.TomatoScanDatabase  Context 2com.ml.tomatoscan.data.database.TomatoScanDatabase  	Migration 2com.ml.tomatoscan.data.database.TomatoScanDatabase  SupportSQLiteDatabase 2com.ml.tomatoscan.data.database.TomatoScanDatabase  TomatoScanDatabase 2com.ml.tomatoscan.data.database.TomatoScanDatabase  Volatile 2com.ml.tomatoscan.data.database.TomatoScanDatabase  analysisDao 2com.ml.tomatoscan.data.database.TomatoScanDatabase  getDatabase 2com.ml.tomatoscan.data.database.TomatoScanDatabase  AnalysisDao <com.ml.tomatoscan.data.database.TomatoScanDatabase.Companion  Context <com.ml.tomatoscan.data.database.TomatoScanDatabase.Companion  	Migration <com.ml.tomatoscan.data.database.TomatoScanDatabase.Companion  SupportSQLiteDatabase <com.ml.tomatoscan.data.database.TomatoScanDatabase.Companion  TomatoScanDatabase <com.ml.tomatoscan.data.database.TomatoScanDatabase.Companion  Volatile <com.ml.tomatoscan.data.database.TomatoScanDatabase.Companion  getDatabase <com.ml.tomatoscan.data.database.TomatoScanDatabase.Companion  
DateConverter *com.ml.tomatoscan.data.database.converters  List *com.ml.tomatoscan.data.database.converters  Long *com.ml.tomatoscan.data.database.converters  String *com.ml.tomatoscan.data.database.converters  StringListConverter *com.ml.tomatoscan.data.database.converters  Date 8com.ml.tomatoscan.data.database.converters.DateConverter  Long 8com.ml.tomatoscan.data.database.converters.DateConverter  
TypeConverter 8com.ml.tomatoscan.data.database.converters.DateConverter  List >com.ml.tomatoscan.data.database.converters.StringListConverter  String >com.ml.tomatoscan.data.database.converters.StringListConverter  
TypeConverter >com.ml.tomatoscan.data.database.converters.StringListConverter  AnalysisDao #com.ml.tomatoscan.data.database.dao  Dao #com.ml.tomatoscan.data.database.dao  Delete #com.ml.tomatoscan.data.database.dao  DiseaseStatistic #com.ml.tomatoscan.data.database.dao  Float #com.ml.tomatoscan.data.database.dao  Insert #com.ml.tomatoscan.data.database.dao  Int #com.ml.tomatoscan.data.database.dao  List #com.ml.tomatoscan.data.database.dao  Long #com.ml.tomatoscan.data.database.dao  OnConflictStrategy #com.ml.tomatoscan.data.database.dao  Query #com.ml.tomatoscan.data.database.dao  SeverityStatistic #com.ml.tomatoscan.data.database.dao  String #com.ml.tomatoscan.data.database.dao  Update #com.ml.tomatoscan.data.database.dao  java #com.ml.tomatoscan.data.database.dao  AnalysisEntity /com.ml.tomatoscan.data.database.dao.AnalysisDao  Delete /com.ml.tomatoscan.data.database.dao.AnalysisDao  DiseaseStatistic /com.ml.tomatoscan.data.database.dao.AnalysisDao  Float /com.ml.tomatoscan.data.database.dao.AnalysisDao  Flow /com.ml.tomatoscan.data.database.dao.AnalysisDao  Insert /com.ml.tomatoscan.data.database.dao.AnalysisDao  Int /com.ml.tomatoscan.data.database.dao.AnalysisDao  List /com.ml.tomatoscan.data.database.dao.AnalysisDao  Long /com.ml.tomatoscan.data.database.dao.AnalysisDao  OnConflictStrategy /com.ml.tomatoscan.data.database.dao.AnalysisDao  Query /com.ml.tomatoscan.data.database.dao.AnalysisDao  SeverityStatistic /com.ml.tomatoscan.data.database.dao.AnalysisDao  String /com.ml.tomatoscan.data.database.dao.AnalysisDao  Update /com.ml.tomatoscan.data.database.dao.AnalysisDao  deleteAllAnalyses /com.ml.tomatoscan.data.database.dao.AnalysisDao  deleteAnalysis /com.ml.tomatoscan.data.database.dao.AnalysisDao  findAnalysisByTimestamp /com.ml.tomatoscan.data.database.dao.AnalysisDao  getAnalysisCount /com.ml.tomatoscan.data.database.dao.AnalysisDao  getAverageConfidence /com.ml.tomatoscan.data.database.dao.AnalysisDao  getDiseaseStatistics /com.ml.tomatoscan.data.database.dao.AnalysisDao  getRecentAnalyses /com.ml.tomatoscan.data.database.dao.AnalysisDao  getSeverityStatistics /com.ml.tomatoscan.data.database.dao.AnalysisDao  insertAnalysis /com.ml.tomatoscan.data.database.dao.AnalysisDao  java /com.ml.tomatoscan.data.database.dao.AnalysisDao  Int 4com.ml.tomatoscan.data.database.dao.DiseaseStatistic  String 4com.ml.tomatoscan.data.database.dao.DiseaseStatistic  Int 5com.ml.tomatoscan.data.database.dao.SeverityStatistic  String 5com.ml.tomatoscan.data.database.dao.SeverityStatistic  AnalysisEntity (com.ml.tomatoscan.data.database.entities  Any (com.ml.tomatoscan.data.database.entities  Boolean (com.ml.tomatoscan.data.database.entities  	ByteArray (com.ml.tomatoscan.data.database.entities  
DateConverter (com.ml.tomatoscan.data.database.entities  Float (com.ml.tomatoscan.data.database.entities  Int (com.ml.tomatoscan.data.database.entities  List (com.ml.tomatoscan.data.database.entities  Long (com.ml.tomatoscan.data.database.entities  String (com.ml.tomatoscan.data.database.entities  StringListConverter (com.ml.tomatoscan.data.database.entities  Any 7com.ml.tomatoscan.data.database.entities.AnalysisEntity  Boolean 7com.ml.tomatoscan.data.database.entities.AnalysisEntity  	ByteArray 7com.ml.tomatoscan.data.database.entities.AnalysisEntity  Date 7com.ml.tomatoscan.data.database.entities.AnalysisEntity  Float 7com.ml.tomatoscan.data.database.entities.AnalysisEntity  Int 7com.ml.tomatoscan.data.database.entities.AnalysisEntity  List 7com.ml.tomatoscan.data.database.entities.AnalysisEntity  Long 7com.ml.tomatoscan.data.database.entities.AnalysisEntity  
PrimaryKey 7com.ml.tomatoscan.data.database.entities.AnalysisEntity  String 7com.ml.tomatoscan.data.database.entities.AnalysisEntity  getLET 7com.ml.tomatoscan.data.database.entities.AnalysisEntity  getLet 7com.ml.tomatoscan.data.database.entities.AnalysisEntity  id 7com.ml.tomatoscan.data.database.entities.AnalysisEntity  	imagePath 7com.ml.tomatoscan.data.database.entities.AnalysisEntity  let 7com.ml.tomatoscan.data.database.entities.AnalysisEntity  Float com.ml.tomatoscan.models  List com.ml.tomatoscan.models  Long com.ml.tomatoscan.models  
ScanResult com.ml.tomatoscan.models  String com.ml.tomatoscan.models  Float #com.ml.tomatoscan.models.ScanResult  List #com.ml.tomatoscan.models.ScanResult  Long #com.ml.tomatoscan.models.ScanResult  String #com.ml.tomatoscan.models.ScanResult  
confidence #com.ml.tomatoscan.models.ScanResult  description #com.ml.tomatoscan.models.ScanResult  diseaseDetected #com.ml.tomatoscan.models.ScanResult  preventionMeasures #com.ml.tomatoscan.models.ScanResult  quality #com.ml.tomatoscan.models.ScanResult  recommendations #com.ml.tomatoscan.models.ScanResult  severity #com.ml.tomatoscan.models.ScanResult  	timestamp #com.ml.tomatoscan.models.ScanResult  treatmentOptions #com.ml.tomatoscan.models.ScanResult  LoginScreen com.ml.tomatoscan.ui.auth  RegisterScreen com.ml.tomatoscan.ui.auth  	Analytics com.ml.tomatoscan.ui.navigation  
BottomNavItem com.ml.tomatoscan.ui.navigation  	CameraAlt com.ml.tomatoscan.ui.navigation  History com.ml.tomatoscan.ui.navigation  Home com.ml.tomatoscan.ui.navigation  Settings com.ml.tomatoscan.ui.navigation  String com.ml.tomatoscan.ui.navigation  	Analytics -com.ml.tomatoscan.ui.navigation.BottomNavItem  
BottomNavItem -com.ml.tomatoscan.ui.navigation.BottomNavItem  	CameraAlt -com.ml.tomatoscan.ui.navigation.BottomNavItem  History -com.ml.tomatoscan.ui.navigation.BottomNavItem  Home -com.ml.tomatoscan.ui.navigation.BottomNavItem  Icons -com.ml.tomatoscan.ui.navigation.BottomNavItem  ImageVector -com.ml.tomatoscan.ui.navigation.BottomNavItem  Settings -com.ml.tomatoscan.ui.navigation.BottomNavItem  String -com.ml.tomatoscan.ui.navigation.BottomNavItem  	CameraAlt 6com.ml.tomatoscan.ui.navigation.BottomNavItem.Analysis  Icons 6com.ml.tomatoscan.ui.navigation.BottomNavItem.Analysis  	Analytics 7com.ml.tomatoscan.ui.navigation.BottomNavItem.Analytics  Icons 7com.ml.tomatoscan.ui.navigation.BottomNavItem.Analytics  Home 7com.ml.tomatoscan.ui.navigation.BottomNavItem.Dashboard  Icons 7com.ml.tomatoscan.ui.navigation.BottomNavItem.Dashboard  History 5com.ml.tomatoscan.ui.navigation.BottomNavItem.History  Icons 5com.ml.tomatoscan.ui.navigation.BottomNavItem.History  Icons 6com.ml.tomatoscan.ui.navigation.BottomNavItem.Settings  Settings 6com.ml.tomatoscan.ui.navigation.BottomNavItem.Settings  AboutDialog com.ml.tomatoscan.ui.screens  AnalysisInProgressScreen com.ml.tomatoscan.ui.screens  AnalysisScreen com.ml.tomatoscan.ui.screens  AnalyticsChart com.ml.tomatoscan.ui.screens  AnalyticsScreen com.ml.tomatoscan.ui.screens  AnalyticsSummary com.ml.tomatoscan.ui.screens  Boolean com.ml.tomatoscan.ui.screens  	BottomBar com.ml.tomatoscan.ui.screens  BottomNavGraph com.ml.tomatoscan.ui.screens  CaptureImageScreen com.ml.tomatoscan.ui.screens  ClearAllConfirmationDialog com.ml.tomatoscan.ui.screens  
Composable com.ml.tomatoscan.ui.screens  DashboardHeader com.ml.tomatoscan.ui.screens  DashboardScreen com.ml.tomatoscan.ui.screens  DeleteConfirmationDialog com.ml.tomatoscan.ui.screens  	DetailRow com.ml.tomatoscan.ui.screens  
DetailSection com.ml.tomatoscan.ui.screens  EmptyHistoryState com.ml.tomatoscan.ui.screens  ExperimentalMaterial3Api com.ml.tomatoscan.ui.screens  ExperimentalMaterialApi com.ml.tomatoscan.ui.screens  HistoryContent com.ml.tomatoscan.ui.screens  HistoryItem com.ml.tomatoscan.ui.screens  
HistoryScreen com.ml.tomatoscan.ui.screens  ImagePreview com.ml.tomatoscan.ui.screens  List com.ml.tomatoscan.ui.screens  LoadingHistoryIndicator com.ml.tomatoscan.ui.screens  
MainScreen com.ml.tomatoscan.ui.screens  NameChangeDialog com.ml.tomatoscan.ui.screens  OptIn com.ml.tomatoscan.ui.screens  QuickActionCard com.ml.tomatoscan.ui.screens  QuickActionsSection com.ml.tomatoscan.ui.screens  RecentScansSection com.ml.tomatoscan.ui.screens  ScanHistoryChart com.ml.tomatoscan.ui.screens  ScanResultDetailDialog com.ml.tomatoscan.ui.screens  SettingsItem com.ml.tomatoscan.ui.screens  SettingsItemRow com.ml.tomatoscan.ui.screens  SettingsScreen com.ml.tomatoscan.ui.screens  SettingsSection com.ml.tomatoscan.ui.screens  SeverityChip com.ml.tomatoscan.ui.screens  SplashScreen com.ml.tomatoscan.ui.screens  StatCard com.ml.tomatoscan.ui.screens  StatsSection com.ml.tomatoscan.ui.screens  String com.ml.tomatoscan.ui.screens  SummaryCard com.ml.tomatoscan.ui.screens  ThemeDialog com.ml.tomatoscan.ui.screens  ThemeOption com.ml.tomatoscan.ui.screens  Unit com.ml.tomatoscan.ui.screens  Uri com.ml.tomatoscan.ui.screens  UriSaver com.ml.tomatoscan.ui.screens  androidx com.ml.tomatoscan.ui.screens  getHistoryDiseaseColor com.ml.tomatoscan.ui.screens  
isNotEmpty com.ml.tomatoscan.ui.screens  Boolean )com.ml.tomatoscan.ui.screens.SettingsItem  Color )com.ml.tomatoscan.ui.screens.SettingsItem  ImageVector )com.ml.tomatoscan.ui.screens.SettingsItem  String )com.ml.tomatoscan.ui.screens.SettingsItem  Unit )com.ml.tomatoscan.ui.screens.SettingsItem  ActionButton %com.ml.tomatoscan.ui.screens.analysis  
ActionButtons %com.ml.tomatoscan.ui.screens.analysis  AnalysisContent %com.ml.tomatoscan.ui.screens.analysis  CameraGuideOverlay %com.ml.tomatoscan.ui.screens.analysis  
CameraPreview %com.ml.tomatoscan.ui.screens.analysis  
CaptureButton %com.ml.tomatoscan.ui.screens.analysis  
DetailSection %com.ml.tomatoscan.ui.screens.analysis  DiseaseAnalysisDetails %com.ml.tomatoscan.ui.screens.analysis  List %com.ml.tomatoscan.ui.screens.analysis  QualityDetails %com.ml.tomatoscan.ui.screens.analysis  ResultSectionCard %com.ml.tomatoscan.ui.screens.analysis  ScanResultDetails %com.ml.tomatoscan.ui.screens.analysis  String %com.ml.tomatoscan.ui.screens.analysis  Unit %com.ml.tomatoscan.ui.screens.analysis  ViewfinderOverlay %com.ml.tomatoscan.ui.screens.analysis  getCameraProvider %com.ml.tomatoscan.ui.screens.analysis  getDiseaseColor %com.ml.tomatoscan.ui.screens.analysis  getQualityColor %com.ml.tomatoscan.ui.screens.analysis  getQualityIcon %com.ml.tomatoscan.ui.screens.analysis  	takePhoto %com.ml.tomatoscan.ui.screens.analysis  Boolean com.ml.tomatoscan.ui.theme  DarkColorScheme com.ml.tomatoscan.ui.theme  DarkGray com.ml.tomatoscan.ui.theme  	DarkGreen com.ml.tomatoscan.ui.theme  LightColorScheme com.ml.tomatoscan.ui.theme  
LightGreen com.ml.tomatoscan.ui.theme  	LimeGreen com.ml.tomatoscan.ui.theme  OffWhite com.ml.tomatoscan.ui.theme  	TomatoRed com.ml.tomatoscan.ui.theme  TomatoScanTheme com.ml.tomatoscan.ui.theme  
Typography com.ml.tomatoscan.ui.theme  Unit com.ml.tomatoscan.ui.theme  White com.ml.tomatoscan.ui.theme  CrashHandler com.ml.tomatoscan.utils  DatabaseImageFetcher com.ml.tomatoscan.utils  String com.ml.tomatoscan.utils  Thread com.ml.tomatoscan.utils  	Throwable com.ml.tomatoscan.utils  android com.ml.tomatoscan.utils  
toDrawable com.ml.tomatoscan.utils  Context $com.ml.tomatoscan.utils.CrashHandler  CrashHandler $com.ml.tomatoscan.utils.CrashHandler  Thread $com.ml.tomatoscan.utils.CrashHandler  	Throwable $com.ml.tomatoscan.utils.CrashHandler  Context .com.ml.tomatoscan.utils.CrashHandler.Companion  CrashHandler .com.ml.tomatoscan.utils.CrashHandler.Companion  Thread .com.ml.tomatoscan.utils.CrashHandler.Companion  	Throwable .com.ml.tomatoscan.utils.CrashHandler.Companion  Context ,com.ml.tomatoscan.utils.DatabaseImageFetcher  Factory ,com.ml.tomatoscan.utils.DatabaseImageFetcher  FetchResult ,com.ml.tomatoscan.utils.DatabaseImageFetcher  Fetcher ,com.ml.tomatoscan.utils.DatabaseImageFetcher  ImageLoader ,com.ml.tomatoscan.utils.DatabaseImageFetcher  Options ,com.ml.tomatoscan.utils.DatabaseImageFetcher  String ,com.ml.tomatoscan.utils.DatabaseImageFetcher  Context 4com.ml.tomatoscan.utils.DatabaseImageFetcher.Factory  Fetcher 4com.ml.tomatoscan.utils.DatabaseImageFetcher.Factory  ImageLoader 4com.ml.tomatoscan.utils.DatabaseImageFetcher.Factory  Options 4com.ml.tomatoscan.utils.DatabaseImageFetcher.Factory  String 4com.ml.tomatoscan.utils.DatabaseImageFetcher.Factory  Boolean com.ml.tomatoscan.viewmodels  Class com.ml.tomatoscan.viewmodels  Context com.ml.tomatoscan.viewmodels  DatabaseImageFetcher com.ml.tomatoscan.viewmodels  FirebaseData com.ml.tomatoscan.viewmodels  	GeminiApi com.ml.tomatoscan.viewmodels  HistoryRepository com.ml.tomatoscan.viewmodels  ImageLoader com.ml.tomatoscan.viewmodels  List com.ml.tomatoscan.viewmodels  MutableStateFlow com.ml.tomatoscan.viewmodels  SharingStarted com.ml.tomatoscan.viewmodels  String com.ml.tomatoscan.viewmodels  TomatoScanViewModel com.ml.tomatoscan.viewmodels  
UserViewModel com.ml.tomatoscan.viewmodels  UserViewModelFactory com.ml.tomatoscan.viewmodels  _isHistoryLoading com.ml.tomatoscan.viewmodels  com com.ml.tomatoscan.viewmodels  	emptyList com.ml.tomatoscan.viewmodels  onEach com.ml.tomatoscan.viewmodels  onStart com.ml.tomatoscan.viewmodels  stateIn com.ml.tomatoscan.viewmodels  viewModelScope com.ml.tomatoscan.viewmodels  Application 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  Bitmap 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  Boolean 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  DatabaseImageFetcher 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  FirebaseData 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  	GeminiApi 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  HistoryRepository 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  ImageLoader 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  List 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  MutableStateFlow 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  
ScanResult 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  SharingStarted 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  	StateFlow 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  Uri 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  _analysisImageUri 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  _isHistoryLoading 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  
_isLoading 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  
_isRefreshing 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  _scanResult 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  com 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  	emptyList 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  getEMPTYList 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  getEmptyList 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  	getONEach 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  
getONStart 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  	getOnEach 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  
getOnStart 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  
getSTATEIn 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  
getStateIn 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  getVIEWModelScope 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  getViewModelScope 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  historyRepository 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  invoke 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  onEach 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  onStart 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  stateIn 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  viewModelScope 0com.ml.tomatoscan.viewmodels.TomatoScanViewModel  Application *com.ml.tomatoscan.viewmodels.UserViewModel  Context *com.ml.tomatoscan.viewmodels.UserViewModel  MutableStateFlow *com.ml.tomatoscan.viewmodels.UserViewModel  	StateFlow *com.ml.tomatoscan.viewmodels.UserViewModel  String *com.ml.tomatoscan.viewmodels.UserViewModel  Uri *com.ml.tomatoscan.viewmodels.UserViewModel  	_userName *com.ml.tomatoscan.viewmodels.UserViewModel  _userProfilePictureUri *com.ml.tomatoscan.viewmodels.UserViewModel  Application 1com.ml.tomatoscan.viewmodels.UserViewModelFactory  Class 1com.ml.tomatoscan.viewmodels.UserViewModelFactory  	ViewModel 1com.ml.tomatoscan.viewmodels.UserViewModelFactory  ByteArrayOutputStream java.io  File java.io  FileOutputStream java.io  PrintWriter java.io  StringWriter java.io  apply java.io.File  exists java.io.File  getAPPLY java.io.File  getApply java.io.File  mkdirs java.io.File  AnalysisEntity 	java.lang  	ByteArray 	java.lang  Class 	java.lang  Context 	java.lang  DatabaseImageFetcher 	java.lang  Date 	java.lang  
DateConverter 	java.lang  Dispatchers 	java.lang  	Exception 	java.lang  ExperimentalAnimationApi 	java.lang  ExperimentalMaterial3Api 	java.lang  ExperimentalMaterialApi 	java.lang  File 	java.lang  FirebaseAuth 	java.lang  FirebaseData 	java.lang  FirebaseFirestore 	java.lang  FirebaseStorage 	java.lang  	GeminiApi 	java.lang  GenerativeModel 	java.lang  HistoryRepository 	java.lang  ImageLoader 	java.lang  ImageStorageHelper 	java.lang  Log 	java.lang  MutableStateFlow 	java.lang  OnConflictStrategy 	java.lang  SharingStarted 	java.lang  StringListConverter 	java.lang  Thread 	java.lang  TomatoScanDatabase 	java.lang  Uri 	java.lang  _isHistoryLoading 	java.lang  analysisDao 	java.lang  android 	java.lang  androidx 	java.lang  apply 	java.lang  cleanupOldAnalyses 	java.lang  com 	java.lang  	emptyList 	java.lang  emptyMap 	java.lang  first 	java.lang  forEach 	java.lang  getValue 	java.lang  imageStorageHelper 	java.lang  
isNotEmpty 	java.lang  java 	java.lang  lazy 	java.lang  let 	java.lang  mapOf 	java.lang  onEach 	java.lang  onStart 	java.lang  provideDelegate 	java.lang  stateIn 	java.lang  to 	java.lang  withContext 	java.lang  UncaughtExceptionHandler java.lang.Thread  SimpleDateFormat 	java.text  AnalysisEntity 	java.util  	ByteArray 	java.util  
Composable 	java.util  Date 	java.util  Dispatchers 	java.util  	Exception 	java.util  ExperimentalMaterial3Api 	java.util  ExperimentalMaterialApi 	java.util  File 	java.util  ImageStorageHelper 	java.util  Locale 	java.util  Log 	java.util  TomatoScanDatabase 	java.util  UUID 	java.util  analysisDao 	java.util  androidx 	java.util  apply 	java.util  cleanupOldAnalyses 	java.util  emptyMap 	java.util  first 	java.util  forEach 	java.util  getValue 	java.util  imageStorageHelper 	java.util  lazy 	java.util  let 	java.util  mapOf 	java.util  provideDelegate 	java.util  to 	java.util  withContext 	java.util  AnalysisEntity kotlin  Any kotlin  Array kotlin  Boolean kotlin  	ByteArray kotlin  Class kotlin  Context kotlin  DatabaseImageFetcher kotlin  Date kotlin  
DateConverter kotlin  Dispatchers kotlin  Double kotlin  	Exception kotlin  ExperimentalAnimationApi kotlin  ExperimentalMaterial3Api kotlin  ExperimentalMaterialApi kotlin  File kotlin  FirebaseAuth kotlin  FirebaseData kotlin  FirebaseFirestore kotlin  FirebaseStorage kotlin  Float kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	GeminiApi kotlin  GenerativeModel kotlin  HistoryRepository kotlin  ImageLoader kotlin  ImageStorageHelper kotlin  Int kotlin  Lazy kotlin  Log kotlin  Long kotlin  MutableStateFlow kotlin  Nothing kotlin  OnConflictStrategy kotlin  OptIn kotlin  Pair kotlin  SharingStarted kotlin  String kotlin  StringListConverter kotlin  Thread kotlin  	Throwable kotlin  TomatoScanDatabase kotlin  Unit kotlin  Uri kotlin  Volatile kotlin  _isHistoryLoading kotlin  analysisDao kotlin  android kotlin  androidx kotlin  apply kotlin  arrayOf kotlin  cleanupOldAnalyses kotlin  com kotlin  	emptyList kotlin  emptyMap kotlin  first kotlin  forEach kotlin  getValue kotlin  imageStorageHelper kotlin  
isNotEmpty kotlin  java kotlin  lazy kotlin  let kotlin  mapOf kotlin  onEach kotlin  onStart kotlin  provideDelegate kotlin  stateIn kotlin  to kotlin  withContext kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getSP 
kotlin.Int  getSp 
kotlin.Int  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  
getISNotEmpty 
kotlin.String  
getIsNotEmpty 
kotlin.String  getLET 
kotlin.String  getLet 
kotlin.String  getTO 
kotlin.String  getTo 
kotlin.String  
isNotEmpty 
kotlin.String  AnalysisEntity kotlin.annotation  	ByteArray kotlin.annotation  Class kotlin.annotation  Context kotlin.annotation  DatabaseImageFetcher kotlin.annotation  Date kotlin.annotation  
DateConverter kotlin.annotation  Dispatchers kotlin.annotation  	Exception kotlin.annotation  ExperimentalAnimationApi kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  ExperimentalMaterialApi kotlin.annotation  File kotlin.annotation  FirebaseAuth kotlin.annotation  FirebaseData kotlin.annotation  FirebaseFirestore kotlin.annotation  FirebaseStorage kotlin.annotation  	GeminiApi kotlin.annotation  GenerativeModel kotlin.annotation  HistoryRepository kotlin.annotation  ImageLoader kotlin.annotation  ImageStorageHelper kotlin.annotation  Log kotlin.annotation  MutableStateFlow kotlin.annotation  OnConflictStrategy kotlin.annotation  Pair kotlin.annotation  SharingStarted kotlin.annotation  StringListConverter kotlin.annotation  Thread kotlin.annotation  TomatoScanDatabase kotlin.annotation  Uri kotlin.annotation  Volatile kotlin.annotation  _isHistoryLoading kotlin.annotation  analysisDao kotlin.annotation  android kotlin.annotation  androidx kotlin.annotation  apply kotlin.annotation  cleanupOldAnalyses kotlin.annotation  com kotlin.annotation  	emptyList kotlin.annotation  emptyMap kotlin.annotation  first kotlin.annotation  forEach kotlin.annotation  getValue kotlin.annotation  imageStorageHelper kotlin.annotation  
isNotEmpty kotlin.annotation  java kotlin.annotation  lazy kotlin.annotation  let kotlin.annotation  mapOf kotlin.annotation  onEach kotlin.annotation  onStart kotlin.annotation  provideDelegate kotlin.annotation  stateIn kotlin.annotation  to kotlin.annotation  withContext kotlin.annotation  AnalysisEntity kotlin.collections  	ByteArray kotlin.collections  Class kotlin.collections  Context kotlin.collections  DatabaseImageFetcher kotlin.collections  Date kotlin.collections  
DateConverter kotlin.collections  Dispatchers kotlin.collections  	Exception kotlin.collections  ExperimentalAnimationApi kotlin.collections  ExperimentalMaterial3Api kotlin.collections  ExperimentalMaterialApi kotlin.collections  File kotlin.collections  FirebaseAuth kotlin.collections  FirebaseData kotlin.collections  FirebaseFirestore kotlin.collections  FirebaseStorage kotlin.collections  	GeminiApi kotlin.collections  GenerativeModel kotlin.collections  HistoryRepository kotlin.collections  ImageLoader kotlin.collections  ImageStorageHelper kotlin.collections  List kotlin.collections  Log kotlin.collections  Map kotlin.collections  MutableStateFlow kotlin.collections  OnConflictStrategy kotlin.collections  Pair kotlin.collections  SharingStarted kotlin.collections  StringListConverter kotlin.collections  Thread kotlin.collections  TomatoScanDatabase kotlin.collections  Uri kotlin.collections  Volatile kotlin.collections  _isHistoryLoading kotlin.collections  analysisDao kotlin.collections  android kotlin.collections  androidx kotlin.collections  apply kotlin.collections  cleanupOldAnalyses kotlin.collections  com kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  first kotlin.collections  forEach kotlin.collections  getValue kotlin.collections  imageStorageHelper kotlin.collections  
isNotEmpty kotlin.collections  java kotlin.collections  lazy kotlin.collections  let kotlin.collections  mapOf kotlin.collections  onEach kotlin.collections  onStart kotlin.collections  provideDelegate kotlin.collections  stateIn kotlin.collections  to kotlin.collections  withContext kotlin.collections  AnalysisEntity kotlin.comparisons  	ByteArray kotlin.comparisons  Class kotlin.comparisons  Context kotlin.comparisons  DatabaseImageFetcher kotlin.comparisons  Date kotlin.comparisons  
DateConverter kotlin.comparisons  Dispatchers kotlin.comparisons  	Exception kotlin.comparisons  ExperimentalAnimationApi kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  ExperimentalMaterialApi kotlin.comparisons  File kotlin.comparisons  FirebaseAuth kotlin.comparisons  FirebaseData kotlin.comparisons  FirebaseFirestore kotlin.comparisons  FirebaseStorage kotlin.comparisons  	GeminiApi kotlin.comparisons  GenerativeModel kotlin.comparisons  HistoryRepository kotlin.comparisons  ImageLoader kotlin.comparisons  ImageStorageHelper kotlin.comparisons  Log kotlin.comparisons  MutableStateFlow kotlin.comparisons  OnConflictStrategy kotlin.comparisons  Pair kotlin.comparisons  SharingStarted kotlin.comparisons  StringListConverter kotlin.comparisons  Thread kotlin.comparisons  TomatoScanDatabase kotlin.comparisons  Uri kotlin.comparisons  Volatile kotlin.comparisons  _isHistoryLoading kotlin.comparisons  analysisDao kotlin.comparisons  android kotlin.comparisons  androidx kotlin.comparisons  apply kotlin.comparisons  cleanupOldAnalyses kotlin.comparisons  com kotlin.comparisons  	emptyList kotlin.comparisons  emptyMap kotlin.comparisons  first kotlin.comparisons  forEach kotlin.comparisons  getValue kotlin.comparisons  imageStorageHelper kotlin.comparisons  
isNotEmpty kotlin.comparisons  java kotlin.comparisons  lazy kotlin.comparisons  let kotlin.comparisons  mapOf kotlin.comparisons  onEach kotlin.comparisons  onStart kotlin.comparisons  provideDelegate kotlin.comparisons  stateIn kotlin.comparisons  to kotlin.comparisons  withContext kotlin.comparisons  SuspendFunction1 kotlin.coroutines  resume kotlin.coroutines  suspendCoroutine kotlin.coroutines  AnalysisEntity 	kotlin.io  	ByteArray 	kotlin.io  Class 	kotlin.io  Context 	kotlin.io  DatabaseImageFetcher 	kotlin.io  Date 	kotlin.io  
DateConverter 	kotlin.io  Dispatchers 	kotlin.io  	Exception 	kotlin.io  ExperimentalAnimationApi 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  ExperimentalMaterialApi 	kotlin.io  File 	kotlin.io  FirebaseAuth 	kotlin.io  FirebaseData 	kotlin.io  FirebaseFirestore 	kotlin.io  FirebaseStorage 	kotlin.io  	GeminiApi 	kotlin.io  GenerativeModel 	kotlin.io  HistoryRepository 	kotlin.io  ImageLoader 	kotlin.io  ImageStorageHelper 	kotlin.io  Log 	kotlin.io  MutableStateFlow 	kotlin.io  OnConflictStrategy 	kotlin.io  Pair 	kotlin.io  SharingStarted 	kotlin.io  StringListConverter 	kotlin.io  Thread 	kotlin.io  TomatoScanDatabase 	kotlin.io  Uri 	kotlin.io  Volatile 	kotlin.io  _isHistoryLoading 	kotlin.io  analysisDao 	kotlin.io  android 	kotlin.io  androidx 	kotlin.io  apply 	kotlin.io  cleanupOldAnalyses 	kotlin.io  com 	kotlin.io  	emptyList 	kotlin.io  emptyMap 	kotlin.io  first 	kotlin.io  forEach 	kotlin.io  getValue 	kotlin.io  imageStorageHelper 	kotlin.io  
isNotEmpty 	kotlin.io  java 	kotlin.io  lazy 	kotlin.io  let 	kotlin.io  mapOf 	kotlin.io  onEach 	kotlin.io  onStart 	kotlin.io  provideDelegate 	kotlin.io  stateIn 	kotlin.io  to 	kotlin.io  withContext 	kotlin.io  AnalysisEntity 
kotlin.jvm  	ByteArray 
kotlin.jvm  Class 
kotlin.jvm  Context 
kotlin.jvm  DatabaseImageFetcher 
kotlin.jvm  Date 
kotlin.jvm  
DateConverter 
kotlin.jvm  Dispatchers 
kotlin.jvm  	Exception 
kotlin.jvm  ExperimentalAnimationApi 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  ExperimentalMaterialApi 
kotlin.jvm  File 
kotlin.jvm  FirebaseAuth 
kotlin.jvm  FirebaseData 
kotlin.jvm  FirebaseFirestore 
kotlin.jvm  FirebaseStorage 
kotlin.jvm  	GeminiApi 
kotlin.jvm  GenerativeModel 
kotlin.jvm  HistoryRepository 
kotlin.jvm  ImageLoader 
kotlin.jvm  ImageStorageHelper 
kotlin.jvm  Log 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  Pair 
kotlin.jvm  SharingStarted 
kotlin.jvm  StringListConverter 
kotlin.jvm  Thread 
kotlin.jvm  TomatoScanDatabase 
kotlin.jvm  Uri 
kotlin.jvm  Volatile 
kotlin.jvm  _isHistoryLoading 
kotlin.jvm  analysisDao 
kotlin.jvm  android 
kotlin.jvm  androidx 
kotlin.jvm  apply 
kotlin.jvm  cleanupOldAnalyses 
kotlin.jvm  com 
kotlin.jvm  	emptyList 
kotlin.jvm  emptyMap 
kotlin.jvm  first 
kotlin.jvm  forEach 
kotlin.jvm  getValue 
kotlin.jvm  imageStorageHelper 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  java 
kotlin.jvm  lazy 
kotlin.jvm  let 
kotlin.jvm  mapOf 
kotlin.jvm  onEach 
kotlin.jvm  onStart 
kotlin.jvm  provideDelegate 
kotlin.jvm  stateIn 
kotlin.jvm  to 
kotlin.jvm  withContext 
kotlin.jvm  AnalysisEntity 
kotlin.ranges  	ByteArray 
kotlin.ranges  Class 
kotlin.ranges  Context 
kotlin.ranges  DatabaseImageFetcher 
kotlin.ranges  Date 
kotlin.ranges  
DateConverter 
kotlin.ranges  Dispatchers 
kotlin.ranges  	Exception 
kotlin.ranges  ExperimentalAnimationApi 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  ExperimentalMaterialApi 
kotlin.ranges  File 
kotlin.ranges  FirebaseAuth 
kotlin.ranges  FirebaseData 
kotlin.ranges  FirebaseFirestore 
kotlin.ranges  FirebaseStorage 
kotlin.ranges  	GeminiApi 
kotlin.ranges  GenerativeModel 
kotlin.ranges  HistoryRepository 
kotlin.ranges  ImageLoader 
kotlin.ranges  ImageStorageHelper 
kotlin.ranges  Log 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  Pair 
kotlin.ranges  SharingStarted 
kotlin.ranges  StringListConverter 
kotlin.ranges  Thread 
kotlin.ranges  TomatoScanDatabase 
kotlin.ranges  Uri 
kotlin.ranges  Volatile 
kotlin.ranges  _isHistoryLoading 
kotlin.ranges  analysisDao 
kotlin.ranges  android 
kotlin.ranges  androidx 
kotlin.ranges  apply 
kotlin.ranges  cleanupOldAnalyses 
kotlin.ranges  com 
kotlin.ranges  	emptyList 
kotlin.ranges  emptyMap 
kotlin.ranges  first 
kotlin.ranges  forEach 
kotlin.ranges  getValue 
kotlin.ranges  imageStorageHelper 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  java 
kotlin.ranges  lazy 
kotlin.ranges  let 
kotlin.ranges  mapOf 
kotlin.ranges  onEach 
kotlin.ranges  onStart 
kotlin.ranges  provideDelegate 
kotlin.ranges  stateIn 
kotlin.ranges  to 
kotlin.ranges  withContext 
kotlin.ranges  KClass kotlin.reflect  AnalysisEntity kotlin.sequences  	ByteArray kotlin.sequences  Class kotlin.sequences  Context kotlin.sequences  DatabaseImageFetcher kotlin.sequences  Date kotlin.sequences  
DateConverter kotlin.sequences  Dispatchers kotlin.sequences  	Exception kotlin.sequences  ExperimentalAnimationApi kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  ExperimentalMaterialApi kotlin.sequences  File kotlin.sequences  FirebaseAuth kotlin.sequences  FirebaseData kotlin.sequences  FirebaseFirestore kotlin.sequences  FirebaseStorage kotlin.sequences  	GeminiApi kotlin.sequences  GenerativeModel kotlin.sequences  HistoryRepository kotlin.sequences  ImageLoader kotlin.sequences  ImageStorageHelper kotlin.sequences  Log kotlin.sequences  MutableStateFlow kotlin.sequences  OnConflictStrategy kotlin.sequences  Pair kotlin.sequences  SharingStarted kotlin.sequences  StringListConverter kotlin.sequences  Thread kotlin.sequences  TomatoScanDatabase kotlin.sequences  Uri kotlin.sequences  Volatile kotlin.sequences  _isHistoryLoading kotlin.sequences  analysisDao kotlin.sequences  android kotlin.sequences  androidx kotlin.sequences  apply kotlin.sequences  cleanupOldAnalyses kotlin.sequences  com kotlin.sequences  	emptyList kotlin.sequences  emptyMap kotlin.sequences  first kotlin.sequences  forEach kotlin.sequences  getValue kotlin.sequences  imageStorageHelper kotlin.sequences  
isNotEmpty kotlin.sequences  java kotlin.sequences  lazy kotlin.sequences  let kotlin.sequences  mapOf kotlin.sequences  onEach kotlin.sequences  onStart kotlin.sequences  provideDelegate kotlin.sequences  stateIn kotlin.sequences  to kotlin.sequences  withContext kotlin.sequences  AnalysisEntity kotlin.text  	ByteArray kotlin.text  Class kotlin.text  Context kotlin.text  DatabaseImageFetcher kotlin.text  Date kotlin.text  
DateConverter kotlin.text  Dispatchers kotlin.text  	Exception kotlin.text  ExperimentalAnimationApi kotlin.text  ExperimentalMaterial3Api kotlin.text  ExperimentalMaterialApi kotlin.text  File kotlin.text  FirebaseAuth kotlin.text  FirebaseData kotlin.text  FirebaseFirestore kotlin.text  FirebaseStorage kotlin.text  	GeminiApi kotlin.text  GenerativeModel kotlin.text  HistoryRepository kotlin.text  ImageLoader kotlin.text  ImageStorageHelper kotlin.text  Log kotlin.text  MutableStateFlow kotlin.text  OnConflictStrategy kotlin.text  Pair kotlin.text  SharingStarted kotlin.text  StringListConverter kotlin.text  Thread kotlin.text  TomatoScanDatabase kotlin.text  Uri kotlin.text  Volatile kotlin.text  _isHistoryLoading kotlin.text  analysisDao kotlin.text  android kotlin.text  androidx kotlin.text  apply kotlin.text  cleanupOldAnalyses kotlin.text  com kotlin.text  	emptyList kotlin.text  emptyMap kotlin.text  first kotlin.text  forEach kotlin.text  getValue kotlin.text  imageStorageHelper kotlin.text  
isNotEmpty kotlin.text  java kotlin.text  lazy kotlin.text  let kotlin.text  mapOf kotlin.text  onEach kotlin.text  onStart kotlin.text  provideDelegate kotlin.text  stateIn kotlin.text  to kotlin.text  withContext kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  AnalysisEntity !kotlinx.coroutines.CoroutineScope  	ByteArray !kotlinx.coroutines.CoroutineScope  Date !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  analysisDao !kotlinx.coroutines.CoroutineScope  cleanupOldAnalyses !kotlinx.coroutines.CoroutineScope  emptyMap !kotlinx.coroutines.CoroutineScope  first !kotlinx.coroutines.CoroutineScope  getANALYSISDao !kotlinx.coroutines.CoroutineScope  getAnalysisDao !kotlinx.coroutines.CoroutineScope  getCLEANUPOldAnalyses !kotlinx.coroutines.CoroutineScope  getCleanupOldAnalyses !kotlinx.coroutines.CoroutineScope  getEMPTYMap !kotlinx.coroutines.CoroutineScope  getEmptyMap !kotlinx.coroutines.CoroutineScope  getFIRST !kotlinx.coroutines.CoroutineScope  getFirst !kotlinx.coroutines.CoroutineScope  getIMAGEStorageHelper !kotlinx.coroutines.CoroutineScope  getImageStorageHelper !kotlinx.coroutines.CoroutineScope  getLET !kotlinx.coroutines.CoroutineScope  getLet !kotlinx.coroutines.CoroutineScope  getMAPOf !kotlinx.coroutines.CoroutineScope  getMapOf !kotlinx.coroutines.CoroutineScope  getTO !kotlinx.coroutines.CoroutineScope  getTo !kotlinx.coroutines.CoroutineScope  imageStorageHelper !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  mapOf !kotlinx.coroutines.CoroutineScope  to !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  SharingStarted kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  first kotlinx.coroutines.flow  map kotlinx.coroutines.flow  onEach kotlinx.coroutines.flow  onStart kotlinx.coroutines.flow  stateIn kotlinx.coroutines.flow  first kotlinx.coroutines.flow.Flow  getFIRST kotlinx.coroutines.flow.Flow  getFirst kotlinx.coroutines.flow.Flow  	getONEach kotlinx.coroutines.flow.Flow  
getONStart kotlinx.coroutines.flow.Flow  	getOnEach kotlinx.coroutines.flow.Flow  
getOnStart kotlinx.coroutines.flow.Flow  
getSTATEIn kotlinx.coroutines.flow.Flow  
getStateIn kotlinx.coroutines.flow.Flow  onEach kotlinx.coroutines.flow.Flow  onStart kotlinx.coroutines.flow.Flow  stateIn kotlinx.coroutines.flow.Flow  _isHistoryLoading %kotlinx.coroutines.flow.FlowCollector  get_isHistoryLoading %kotlinx.coroutines.flow.FlowCollector  value (kotlinx.coroutines.flow.MutableStateFlow  WhileSubscribed &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed 0kotlinx.coroutines.flow.SharingStarted.Companion  await kotlinx.coroutines.tasks  	Parcelize kotlinx.parcelize  	JSONArray org.json  
JSONObject org.json  Preview #androidx.compose.ui.tooling.preview  BottomBarPreview com.ml.tomatoscan.ui.screens                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   